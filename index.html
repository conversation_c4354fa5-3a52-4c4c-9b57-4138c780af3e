<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MeetAugust Calorie Calculator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #ffffff;
      margin: 0;
      padding: 0;
    }
    header {
      background-color: #F5F6F5;
      padding: 10px;
      color: white;
    }
    .logo {
      float: left;
      font-size: 20px;

    } 
    .nav {
      float: right;
    }
    .nav a {
      color: white;
      margin-left: 20px;
      text-decoration: none;
    }
    .nav a.active {
      color: #20C997;
    }
    .breadcrumb {
      clear: both;
      color: #ccc;
      font-size: 12px;
      margin-top: 5px;
    }
    main {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #007BFF;
      font-size: 24px;
      font-weight: bold;
    }
    p {
      color: #333;
      font-size: 14px;
    }
    .print-link {
      text-align: right;
      margin-bottom: 10px;
    }
    .print-link a {
      color: #007BFF;
      text-decoration: none;
    }
    .form-container {
      background-color: #F0F0F0;
      border: 1px solid #D3D3D3;
      padding: 20px;
    }
    .tabs {
      margin-bottom: 10px;
    }
    .tab-button {
      background-color: #F0F0F0;
      border: 1px solid #D3D3D3;
      padding: 5px 10px;
      cursor: pointer;
    }
    .tab-button.active {
      background-color: white;
      border-bottom: 2px solid #007BFF;
    }
    .form-instruction {
      color: #333;
      font-size: 14px;
      margin-bottom: 20px;
    }
    .form-field {
      margin-bottom: 10px;
    }
    .form-field label {
      display: inline-block;
      width: 100px;
      color: #333;
    }
    .form-field input[type="text"] {
      width: 100px;
      background-color: #F0F0F0;
      border: 1px solid #D3D3D3;
      padding: 5px;
    }
    .form-field select {
      width: 300px;
      background-color: white;
      border: 1px solid #D3D3D3;
      padding: 5px;
    }
    .settings-link {
      margin-bottom: 20px;
    }
    .settings-link a {
      color: #007BFF;
      text-decoration: none;
    }
    .form-buttons {
      text-align: left;
    }
    .calculate-button {
      background-color: #20C997;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      margin-right: 10px;
      cursor: pointer;
    }
    .clear-button {
      background-color: #D3D3D3;
      color: #333;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    .result {
      margin-top: 20px;
      color: #333;
      font-size: 16px;
    }
    .activity-definitions {
      margin-top: 20px;
      color: #333;
      font-size: 12px;
    }
    .activity-definitions ul {
      list-style-type: disc;
      padding-left: 20px;
    }
  </style>
</head>
<body>
  <header>
    <div class="logo">
        <img width="200px" height="100px" src="./assets/august_logo_green_nd4fn9.svg" alt="">
    </div>
    <div class="nav">
      <a href="">Talk to August</a> 
    </div> 
  </header>
  <main>
    <h1>MeetAugust Calorie Calculator</h1>
    <p>The Calorie Calculator can be used to estimate the number of calories a person needs to consume each day. This calculator can also provide some simple guidelines for gaining or losing weight.</p>
    <div class="print-link">
      <a href="#">Print</a>
    </div>
    <section class="form-container">
      <div class="tabs">
        <button class="tab-button">US Units</button>
        <button class="tab-button active">Metric Units</button>
        <button class="tab-button">Other Units</button>
      </div>
      <p class="form-instruction">Modify the values and click the Calculate button to use</p>
      <form id="calorie-form">
        <div class="form-field">
          <label for="age">Age</label>
          <input type="text" id="age" value="25">
          <span>ages 15 - 80</span>
        </div>
        <div class="form-field">
          <label>Gender</label>
          <input type="radio" name="gender" value="male" checked> male
          <input type="radio" name="gender" value="female"> female
        </div>
        <div class="form-field">
          <label for="height">Height</label>
          <input type="text" id="height" value="180">
          <span>cm</span>
        </div>
        <div class="form-field">
          <label for="weight">Weight</label>
          <input type="text" id="weight" value="65">
          <span>kg</span>
        </div>
        <div class="form-field">
          <label for="activity">Activity</label>
          <select id="activity">
            <option value="sedentary">Sedentary: little or no exercise</option>
            <option value="light">Lightly active: light exercise 1-3 days/week</option>
            <option value="moderate" selected>Moderately active: moderate exercise 3-5 days/week</option>
            <option value="very">Very active: hard exercise 6-7 days/week</option>
            <option value="super">Super active: very hard exercise, physical job</option>
          </select>
        </div>
        <div class="settings-link">
          <a href="#">+ Settings</a>
        </div>
        <div class="form-buttons">
          <button type="button" class="calculate-button">Calculate ▶</button>
          <button type="button" class="clear-button">Clear</button>
        </div>
      </form>
    </section>
    <div id="result" class="result"></div>
    <div class="activity-definitions">
      <ul>
        <li>Exercise: 15-30 minutes of elevated heart rate activity.</li>
        <li>Intense exercise: 45-120 minutes of elevated heart rate activity.</li>
        <li>Very intense exercise: 2+ hours of elevated heart rate activity.</li>
      </ul>
    </div>
  </main>
  <script>
    // Tab switching
    var tabs = document.querySelectorAll('.tab-button');
    tabs.forEach(function(tab) {
      tab.addEventListener('click', function() {
        tabs.forEach(function(t) {
          t.classList.remove('active');
        });
        this.classList.add('active');
      });
    });

    // Calculate button
    document.querySelector('.calculate-button').addEventListener('click', function() {
      var age = parseInt(document.getElementById('age').value);
      var gender = document.querySelector('input[name="gender"]:checked').value;
      var height = parseFloat(document.getElementById('height').value);
      var weight = parseFloat(document.getElementById('weight').value);
      var activity = document.getElementById('activity').value;

      var activityFactors = {
        'sedentary': 1.2,
        'light': 1.375,
        'moderate': 1.55,
        'very': 1.725,
        'super': 1.9
      };

      var bmr;
      if (gender === 'male') {
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
      }
      var calories = bmr * activityFactors[activity];
      document.getElementById('result').innerHTML = 'Estimated daily calories: ' + Math.round(calories);
    });

    // Clear button
    document.querySelector('.clear-button').addEventListener('click', function() {
      document.getElementById('calorie-form').reset();
      document.getElementById('result').innerHTML = '';
    });

    // Settings link (placeholder)
    document.querySelector('.settings-link a').addEventListener('click', function(e) {
      e.preventDefault();
      alert('Settings not implemented');
    });
  </script>
</body>
</html>